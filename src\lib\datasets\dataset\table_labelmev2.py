"""
TableLabelMe格式的数据集类，继承现有Table类的基础功能，重写数据加载逻辑。

该数据集类将TableLabelMe格式的数据转换为与COCO格式兼容的数据结构，
确保与现有训练流程完全兼容。
"""

from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

import numpy as np
import json
import random
import os
from typing import Dict, Any, List, Optional, Union

import torch.utils.data as data

from ..parsers import TableLabelMeParser, FileScanner


class Table(data.Dataset):
    """
    TableLabelMe格式数据集类，继承PyTorch Dataset基类。
    
    该类负责加载TableLabelMe格式的数据，并将其转换为与LORE-TSR
    训练流程兼容的标准格式。
    """
    
    # 类属性，与原Table类保持一致
    num_classes = 2
    table_size = 1024
    default_resolution = [1024, 1024]
    mean = np.array([0.40789654, 0.44719302, 0.47026115],
                   dtype=np.float32).reshape(1, 1, 3)
    std = np.array([0.28863828, 0.27408164, 0.27809835],
                  dtype=np.float32).reshape(1, 1, 3)

    def __init__(self, opt, split: str):
        """
        初始化TableLabelMe数据集，设置基本参数和加载数据索引。
        
        Args:
            opt: 配置对象，包含数据路径和训练参数
            split (str): 数据集分割（train/val/test）
        """
        super(Table, self).__init__()
        self.split = split
        self.opt = opt
        
        # 基本参数设置，与原Table类保持一致
        self.max_objs = 300
        self.max_pairs = 900
        self.max_cors = 1200
        
        self.class_name = ['__background__', 'center', 'corner']
        self._valid_ids = [1, 2]
        self.cat_ids = {v: i for i, v in enumerate(self._valid_ids)}
        self.voc_color = [(v // 32 * 64 + 64, (v // 8) % 4 * 64, v % 8 * 32)
                         for v in range(1, self.num_classes + 1)]
        self._data_rng = np.random.RandomState(123)
        self._eig_val = np.array([0.2141788, 0.01817699, 0.00341571],
                                dtype=np.float32)
        self._eig_vec = np.array([
            [-0.58752847, -0.69563484, 0.41340352],
            [-0.5832747, 0.00994535, -0.81221408],
            [-0.56089297, 0.71832671, 0.41158938]
        ], dtype=np.float32)
        
        # 初始化TableLabelMe解析器
        self.parser = TableLabelMeParser()

        # 初始化文件扫描器
        self.file_scanner = FileScanner()

        print('==> initializing TableLabelMe {} data.'.format(split))
        
        # 构建文件索引
        self.file_index = self._build_file_index()
        
        # 加载标注数据
        self._load_annotations()
        
        self.num_samples = len(self.images)
        print('Loaded {} {} samples'.format(split, self.num_samples))

    def _get_data_paths(self) -> List[str]:
        """
        获取数据路径配置（迭代2硬编码版本）。

        Returns:
            List[str]: 数据路径列表

        Note:
            迭代2使用硬编码真实数据路径，迭代4将支持外部配置文件。
        """
        # 使用真实的TableLabelMe数据集路径
        base_path = "D:/workspace/datasets/cf_train_clean/wired_tables_reorganized/TabRecSet_TableLabelMe_fix/chinese"

        # 对于train和val都使用同一个数据集（迭代2简化版本）
        base_paths = {
            'train': [base_path],
            'val': [base_path]
        }
        return base_paths.get(self.split, [])

    def _build_file_index(self) -> Dict[int, Dict[str, str]]:
        """
        构建真实的文件索引，替换固定测试数据。

        Returns:
            Dict[int, Dict[str, str]]: 文件映射字典

        Note:
            使用FileScanner进行真实的目录扫描，替换迭代1的固定测试数据。
        """
        try:
            # 获取数据路径配置
            data_paths = self._get_data_paths()

            if not data_paths:
                print(f'警告：未找到{self.split}分割的数据路径配置')
                return {}

            # 使用FileScanner进行目录扫描
            scan_result = self.file_scanner.scan_directories(data_paths, self.split)

            # 保存扫描统计信息
            self.scan_statistics = scan_result.get('statistics', {})

            # 获取文件索引
            file_index = scan_result.get('file_index', {})

            print(f'目录扫描完成：{self.split}分割，共{len(file_index)}个文件对')
            if hasattr(self, 'scan_statistics'):
                stats = self.scan_statistics
                print(f'扫描统计：{stats.get("valid_pairs", 0)}个有效对，'
                      f'{stats.get("orphan_images", 0)}个孤儿图像，'
                      f'扫描时间：{stats.get("scan_time", 0):.3f}秒')

            return file_index

        except Exception as e:
            print(f'目录扫描失败：{e}')
            print('回退到空索引')
            # 保存错误信息到统计中
            self.scan_statistics = {
                'error': str(e),
                'valid_pairs': 0,
                'total_images': 0,
                'total_annotations': 0
            }
            return {}

    def _load_annotations(self):
        """
        加载并解析所有TableLabelMe标注文件。
        
        Note:
            将TableLabelMe格式转换为与COCO兼容的数据结构，
            确保与现有训练流程完全兼容。
        """
        self.images = []
        self.annotations = {}
        self.image_info = {}
        
        for image_id, file_info in self.file_index.items():
            # MVP版本：跳过实际文件解析，使用模拟数据
            # 在实际使用时，这里会调用解析器处理真实文件
            
            # 模拟图像信息
            self.image_info[image_id] = {
                'id': image_id,
                'file_name': file_info['image_path'],
                'width': 1024,
                'height': 768
            }
            
            # 模拟标注数据（与COCO格式兼容）
            mock_annotations = self._create_mock_annotations(image_id)
            self.annotations[image_id] = mock_annotations
            
            self.images.append(image_id)
    
    def _create_mock_annotations(self, image_id: int) -> List[Dict[str, Any]]:
        """
        创建模拟标注数据，用于MVP版本测试。
        
        Args:
            image_id (int): 图像ID
            
        Returns:
            List[Dict[str, Any]]: 模拟的标注数据列表
            
        Note:
            返回的数据结构与COCO格式完全兼容，确保与现有训练流程无缝集成。
        """
        # 创建一个示例标注，格式与COCO兼容
        mock_annotation = {
            'id': image_id * 1000 + 1,
            'image_id': image_id,
            'category_id': 1,
            'segmentation': [[100.0, 50.0, 200.0, 50.0, 200.0, 100.0, 100.0, 100.0]],
            'logic_axis': [[0, 0, 0, 1]],
            'area': 5000.0,
            'bbox': [100.0, 50.0, 100.0, 50.0],
            'iscrowd': 0,
            'ignore': 0
        }
        
        return [mock_annotation]

    def __len__(self) -> int:
        """
        返回数据集大小。

        Returns:
            int: 数据集中的样本数量
        """
        return self.num_samples

    def _get_image_info(self, index: int) -> Dict[str, Any]:
        """
        获取指定索引的图像信息。

        Args:
            index (int): 图像索引

        Returns:
            Dict[str, Any]: 图像信息字典
        """
        img_id = self.images[index]
        return self.image_info[img_id]

    def getImgIds(self) -> List[int]:
        """
        获取所有图像ID列表，兼容COCO API接口。

        Returns:
            List[int]: 图像ID列表
        """
        return self.images

    def loadImgs(self, ids: List[int]) -> List[Dict[str, Any]]:
        """
        加载指定ID的图像信息，兼容COCO API接口。

        Args:
            ids (List[int]): 图像ID列表

        Returns:
            List[Dict[str, Any]]: 图像信息列表
        """
        return [self.image_info[img_id] for img_id in ids if img_id in self.image_info]

    def getAnnIds(self, imgIds: List[int]) -> List[int]:
        """
        获取指定图像的标注ID列表，兼容COCO API接口。

        Args:
            imgIds (List[int]): 图像ID列表

        Returns:
            List[int]: 标注ID列表
        """
        ann_ids = []
        for img_id in imgIds:
            if img_id in self.annotations:
                for ann in self.annotations[img_id]:
                    ann_ids.append(ann['id'])
        return ann_ids

    def loadAnns(self, ids: List[int]) -> List[Dict[str, Any]]:
        """
        加载指定ID的标注信息，兼容COCO API接口。

        Args:
            ids (List[int]): 标注ID列表

        Returns:
            List[Dict[str, Any]]: 标注信息列表
        """
        annotations = []
        for img_id in self.annotations:
            for ann in self.annotations[img_id]:
                if ann['id'] in ids:
                    annotations.append(ann)
        return annotations

    def _to_float(self, x: Union[int, float]) -> float:
        """
        数值转换为浮点数，保留两位小数。

        Args:
            x (Union[int, float]): 输入数值

        Returns:
            float: 格式化后的浮点数
        """
        return float("{:.2f}".format(x))

    def convert_eval_format(self, all_bboxes: Dict, thresh: float) -> List[Dict[str, Any]]:
        """
        转换评估格式，兼容原Table类接口。

        Args:
            all_bboxes (Dict): 所有边界框预测结果
            thresh (float): 置信度阈值

        Returns:
            List[Dict[str, Any]]: 转换后的检测结果
        """
        detections = []
        for image_id in all_bboxes:
            for cls_ind in all_bboxes[image_id]:
                category_id = self._valid_ids[cls_ind - 1]
                for bbox in all_bboxes[image_id][cls_ind]:
                    if bbox[4] > float(thresh):
                        bbox[2] -= bbox[0]
                        bbox[3] -= bbox[1]
                        score = bbox[4]
                        bbox_out = list(map(self._to_float, bbox[0:4]))

                        detection = {
                            "image_id": int(image_id),
                            "category_id": int(category_id),
                            "bbox": bbox_out,
                            "score": float("{:.2f}".format(score))
                        }
                        if len(bbox) > 5:
                            extreme_points = list(map(self._to_float, bbox[5:13]))
                            detection["extreme_points"] = extreme_points
                        detections.append(detection)
        print('total:', len(detections))
        return detections

    def save_results(self, results: Dict, save_dir: str, thresh: float):
        """
        保存评估结果，兼容原Table类接口。

        Args:
            results (Dict): 预测结果
            save_dir (str): 保存目录
            thresh (float): 置信度阈值
        """
        json.dump(self.convert_eval_format(results, thresh),
                 open('{}/results.json'.format(save_dir), 'w'))

    def run_eval(self, results: Dict, save_dir: str, thresh: float):
        """
        运行评估，兼容原Table类接口。

        Args:
            results (Dict): 预测结果
            save_dir (str): 保存目录
            thresh (float): 置信度阈值

        Note:
            MVP版本暂不实现完整的COCO评估，仅保存结果。
            完整的评估功能将在后续迭代中实现。
        """
        self.save_results(results, save_dir, thresh)
        print('TableLabelMe评估结果已保存到: {}/results.json'.format(save_dir))
        print('注意：MVP版本暂不支持完整的COCO评估指标计算')
